import { Dispatch } from '@reduxjs/toolkit'
import {
  setCreditCardsList,
  setIsLoadingCards,
  setCardResponse,
  setSelectedCardToView,
  setIsLoadingSingleCard,
  setIsLoadingActivateCard,
  setIsLoadingSetPinCard,
  setIsLoadingBranchCards,
  setBranchCardsList,
  setBranchCardsListPagination,
  setIsLoadingResetPinRetries,
  setIsLoadingPan,
} from '@/store/reducers/cards'
import { setNotification } from '@dtbx/store/reducers'
import { secureapi } from '@dtbx/store/utils'
import { setBranchApprovalRequests } from '../reducers/approvals'

export type CardsQueryParams = {
  size: number
  page: number
  cardId?: string
  phoneNumber?: string
  customerName?: string
  domicileBranch?: string
  active?: boolean
  status?: string
  isBlocked?: boolean
  pan?: string
  cardType: string
  cif?: string
}

export type BranchCardParams = {
  pan: string
  phoneNumber: string
  page: number
  size: number
}

export const activateCards = async (
  dispatch: Dispatch,
  data: {
    comments: string
    cardIdentifierId: string
    cardIdentifierType: string
    countryCode: string
  },
  requestType: string
) => {
  dispatch(setIsLoadingActivateCard(true))
  try {
    await secureapi.post(
      `backoffice-bff/cards/activate${requestType === 'super' ? '' : '/make'}`,
      data
    )
    dispatch(setIsLoadingActivateCard(false))

    dispatch(
      setNotification({
        message: `Card ${requestType === 'super' ? 'activated' : 'activation request sent'} successfully`,
        type: 'success',
      })
    )
  } catch (error) {
    console.error('card activation request error:', error)
    dispatch(setIsLoadingActivateCard(false))

    dispatch(
      setNotification({ message: (error as Error).message, type: 'error' })
    )
  }
}

export const completeCardActivation = async (
  dispatch: Dispatch,
  approvalId: string,
  approvalType: 'approve' | 'reject',
  data: { comments: string }
) => {
  dispatch(setIsLoadingActivateCard(true))
  try {
    await secureapi.put(
      `backoffice-bff/cards/activate/${approvalType}/${approvalId}`,
      data
    )
    dispatch(setIsLoadingActivateCard(false))

    dispatch(
      setNotification({
        message: `${approvalType === 'approve' ? 'Activate credit card request has been approved. Customer will be notified via an sms that will include their PIN.' : 'Activate credit card request has been rejected'}`,
        type: 'success',
      })
    )
  } catch (error) {
    console.error('card activation approval completion request error:', error)
    dispatch(setIsLoadingActivateCard(false))

    dispatch(
      setNotification({ message: (error as Error).message, type: 'error' })
    )
  }
}
export const resetCardPin = async (
  dispatch: Dispatch,
  data: {
    comments: string
    cardIdentifierId: string
    cardIdentifierType: string
    countryCode: string
  },
  requestType: string
) => {
  dispatch(setIsLoadingSetPinCard(true))
  try {
    await secureapi.post(
      `backoffice-bff/cards/set-pin${requestType === 'super' ? '' : '/make'}`,
      data
    )
    dispatch(setIsLoadingSetPinCard(false))
    dispatch(
      setNotification({
        message: `Card Pin ${requestType === 'super' ? 'reset' : ' request sent'} successfully`,
        type: 'success',
      })
    )
  } catch (error) {
    console.error('card reset pin request error:', error)
    dispatch(setIsLoadingSetPinCard(false))
    dispatch(
      setNotification({ message: (error as Error).message, type: 'error' })
    )
  }
}
export const completeCardSetPin = async (
  dispatch: Dispatch,
  approvalId: string,
  approvalType: 'approve' | 'reject',
  data: { comments: string }
) => {
  dispatch(setIsLoadingSetPinCard(true))
  try {
    await secureapi.put(
      `backoffice-bff/cards/set-pin/${approvalType}/${approvalId}`,
      data
    )
    dispatch(setIsLoadingSetPinCard(false))

    dispatch(
      setNotification({
        message: `${approvalType === 'approve' ? 'Set pin credit card request has been approved. Customer will be notified via an sms that will include their PIN.' : 'Set pin credit card request has been rejected'}`,
        type: 'success',
      })
    )
  } catch (error) {
    console.error('card set pin approval completion request error:', error)
    dispatch(setIsLoadingSetPinCard(false))

    dispatch(
      setNotification({ message: (error as Error).message, type: 'error' })
    )
  }
}

export const getAllCards = async (
  dispatch: Dispatch,
  params: CardsQueryParams
) => {
  try {
    dispatch(setIsLoadingCards(true))
    const res = await secureapi.get('backoffice-bff/cards', { params })
    dispatch(setCreditCardsList(res.data.data.data))
    dispatch(setCardResponse(res.data.data))
    dispatch(setIsLoadingCards(false))
  } catch (error) {
    dispatch(setIsLoadingCards(false))
    dispatch(
      setNotification({ message: (error as Error).message, type: 'error' })
    )
  }
}

export const getPan = async (
  dispatch: Dispatch,
  data: {
    publicKey: string
    cardIDto: {
      comments?: string
      cardIdentifierId: string
      cardIdentifierType: string
      countryCode: string
    }
  }
) => {
  try {
    const response = await secureapi.post('backoffice-bff/cards/view-pan', data)
    return response.data.data.encryptedPan
  } catch (error) {
    dispatch(
      setNotification({ message: (error as Error).message, type: 'error' })
    )
    dispatch(setIsLoadingPan(false))
  }
}

export const getCardById = async (dispatch: Dispatch, cardId: string) => {
  try {
    dispatch(setIsLoadingSingleCard(true))
    const res = await secureapi.get(`backoffice-bff/cards/${cardId}`)
    dispatch(setSelectedCardToView(res.data.data))
    dispatch(setIsLoadingSingleCard(false))
  } catch (error) {
    dispatch(setIsLoadingSingleCard(false))
    dispatch(
      setNotification({ message: (error as Error).message, type: 'error' })
    )
  }
}

export const getBranchApprovals = async(dispatch: Dispatch, params: BranchCardParams) => {
try {
  const queryParams = new URLSearchParams()
  queryParams.append('pan', params.pan)
  queryParams.append('phoneNumber', params.phoneNumber)
  queryParams.append('page', params.page.toString())
  queryParams.append('size', params.size.toString())

  const res = await secureapi.get(`backoffice-bff/cards/branch-query/approvals?${queryParams.toString()}`)
  dispatch(setBranchApprovalRequests(res.data.data))
} catch (error) {
  dispatch(
    setNotification({ message: (error as Error).message, type: 'error' })
  )
}
}

export const getBranchCardsByPhonePan = async (
  dispatch: Dispatch,
  params: string
) => {
  try {
    dispatch(setIsLoadingBranchCards(true))
    const res = await secureapi.get(`backoffice-bff/cards/branch-query?${params}`)
    dispatch(setBranchCardsList(res.data.data.data))
    const { pageNumber, pageSize, totalElements, totalNumberOfPages } =
      res.data.data
    dispatch(
      setBranchCardsListPagination({
        totalElements,
        totalNumberOfPages,
        pageNumber,
        pageSize,
      })
    )
    dispatch(setIsLoadingBranchCards(false))
  } catch (error) {
    dispatch(setIsLoadingBranchCards(false))
    dispatch(
      setNotification({ message: (error as Error).message, type: 'error' })
    )
  }
}

export const resetCardPinRetryCounter = async (
  dispatch: Dispatch,
  data: {
    comments: string
    cardIdentifierId: string
    cardIdentifierType: string
    countryCode: string
  },
  type: 'make' | 'super'
) => {
  try {
    dispatch(setIsLoadingResetPinRetries(true))
    const response = await secureapi.post(
      `backoffice-bff/cards/reset-pin-try-counter${type === 'make' ? '/make' : ''}`,
      data
    )
    dispatch(
      setNotification({
        message:
          type === 'make'
            ? 'Reset Card PIN Retries request was submitted successfully for approval'
            : 'Reset Card PIN Retries request was successful',
        type: 'success',
      })
    )
    return dispatch(setIsLoadingResetPinRetries(false))
  } catch (error) {
    dispatch(
      setNotification({ message: (error as Error).message, type: 'error' })
    )

    dispatch(setIsLoadingResetPinRetries(false))
  }
}

export const completeResetCardPinRetryCounter = async (
  dispatch: Dispatch,
  data: { comments: string },
  approvalId: string,
  type: 'approve' | 'reject'
) => {
  try {
    const response = await secureapi.put(
      `backoffice-bff/cards/reset-pin-try-counter/${type}/${approvalId}`,
      data
    )
    return response.data.data
  } catch (error) {
    dispatch(
      setNotification({ message: (error as Error).message, type: 'error' })
    )
  }
}
