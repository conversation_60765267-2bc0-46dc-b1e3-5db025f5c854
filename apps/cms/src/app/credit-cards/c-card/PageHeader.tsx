'use client'
import React, { useState, useEffect } from 'react'
import { <PERSON>, Breadcrumbs, <PERSON>, Stack, Typography } from '@mui/material'
import { useCustomRouter } from '@dtbx/ui/hooks'
import {
  CustomActiveChip,
  CustomerInfoChip,
  CustomErrorChip,
} from '@dtbx/ui/components/Chip'
import { useSearchParams } from 'next/navigation'
import {
  CardsChangesLogDrawer,
  CardsApprovalRequestDrawer,
} from '@/app/Drawers'
import { useAppSelector } from '@/store'

const PageHeader = () => {
  const router = useCustomRouter()
  const { selectedCardToView } = useAppSelector((state) => state.cards)
  const { selectedCardApprovalRequest, branchApprovalRequests } = useAppSelector(
    (state) => state.approvals
  )
  const searchParams = useSearchParams()

  const origin = searchParams.get('origin')

  const pendingBranchApproval =
    branchApprovalRequests &&branchApprovalRequests?.find(
      (approval) => approval.status === 'PENDING'
    )

  const currentApproval = (selectedCardApprovalRequest && Object.keys(selectedCardApprovalRequest).length > 0)
    ? selectedCardApprovalRequest
    : pendingBranchApproval

  const [isDrawerOpen, setIsDrawerOpen] = useState(
    origin === 'cardsPage'
      ? false
      : !!currentApproval
  )

  useEffect(() => {
    if (origin !== 'cardsPage') {
      setIsDrawerOpen(
        !!currentApproval && currentApproval.status === 'PENDING'
      )
    }
  }, [selectedCardApprovalRequest, branchApprovalRequests, origin, currentApproval])

  return (
    <Stack
      sx={{
        padding: '1% 2%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderTop: '1px solid #D0D5DD',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          gap: '0.5rem',
          justifyContent: 'center',
          alignItems: 'center',
          alignContent: 'center',
        }}
      >
        <Breadcrumbs>
          <Link
            sx={{
              cursor: 'pointer',
              color: '#555C61',
            }}
            onClick={() => router.push('/credit-cards')}
          >
            Credit Cards
          </Link>
          <Typography sx={{ color: '#000' }}>
            {selectedCardToView && <>{selectedCardToView.customerName}</>}
          </Typography>
        </Breadcrumbs>
        {selectedCardToView.active ? (
          <CustomActiveChip label={'Active'} />
        ) : (
          <CustomErrorChip label={'Inactive'} />
        )}
        {currentApproval && (
          <Box
            onClick={() => setIsDrawerOpen(true)}
            sx={{
              cursor: 'pointer',
              display: 'flex',
            }}
          >
            <CustomerInfoChip
              label={`Pending approval :`}
              requests={[currentApproval?.makerCheckerType?.name || 'Approval request']}
            />
          </Box>
        )}
      </Stack>
      <Stack direction={'row'} spacing={1}>
        {currentApproval && (
          <CardsApprovalRequestDrawer
            origin={origin ?? ''}
            open={isDrawerOpen}
            setOpen={setIsDrawerOpen}
            selectedCardApprovalRequest={currentApproval}
          />
        )}
        <CardsChangesLogDrawer />
      </Stack>
    </Stack>
  )
}

export default PageHeader
