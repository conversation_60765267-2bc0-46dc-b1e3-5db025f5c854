'use client'
import { createSlice, PayloadAction } from '@reduxjs/toolkit'

import {
  IBankBranch,
  IBrokerCreate,
  ICustomerCheck,
  ICustomerDocument,
  ICustomerLoanProfile,
  ILoanProduct,
  ILoanReportResponse,
  ILoanRequest,
  ILoanRequestReportResponse,
  ILoanRequestsSummary,
  ILoanValidation,
  IOrganization,
  IOrganizationLimitResponse,
  IProductCategory,
  IProductCategoryType,
  IRepaymentHistory,
  IRequestCheckReportsResponse,
  ISelectedBrokerExisting,
  ISingleProductValidation,
} from '../interfaces'

interface ILoansState {
  loanRequests: ILoanRequest[]
  organizationLimitReportResponse: IOrganizationLimitResponse
  isLoadingOrgLimitReports: boolean
  isLoadingOrgLimitReportsExport: boolean
  isSuccessOrgLimitReports: boolean
  isSuccessOrgLimitReportsExport: boolean
  loanReportRequestResponse: ILoanRequestReportResponse
  isLoadingReportRequest: boolean
  isSuccessfulReportRequest: boolean
  isLoadingGenerateRequestsReport: boolean
  isSuccessfulGenerateRequestsReport: boolean
  loanReportResponse: ILoanReportResponse
  isLoadingLoanReport: boolean
  isSuccessfulLoanReport: boolean
  isLoadingGenerateLoanReport: boolean
  isSuccessfulGenerateLoanReport: boolean
  requestReports: IRequestCheckReportsResponse
  isLoadingRequestReportsCheck: boolean
  isLoadingRequestReportsCheckExport: boolean
  isSuccessRequestReportsCheck: boolean
  isSuccessRequestReportsCheckExport: boolean

  isLoadingLoans: boolean
  loansCount: number
  pageNumber: number
  pageSize: number
  totalPageCount: number
  isViewingRequest: boolean
  selectedRequest: ILoanRequest
  isLoadingCustomerProfile: boolean
  customerProfile: ICustomerLoanProfile
  customerDocuments: ICustomerDocument[]
  customerChecks: ICustomerCheck[]
  loanRepayments: IRepaymentHistory[]
  isLoadingLoanProducts: boolean
  loanProducts: ILoanProduct[]
  loanProductsSummary: ILoanRequestsSummary
  currentProduct: ILoanProduct
  isLoadingOverrideValidationCheck: boolean
  organizations: IOrganization[]
  organizationsSummary: ILoanRequestsSummary
  editOrganization: IOrganization
  isLoadingOrganizations: boolean
  isLoadingCreateOrganization: boolean
  isLoadingEditOrganization: boolean
  listFilters: { status: string }
  createdProduct: ILoanProduct
  isLoadingCreateProduct: boolean
  loanValidations: ILoanValidation[]
  kycTypeValidations: ILoanValidation[]
  creditTypeValidations: ILoanValidation[]
  productCategories: IProductCategory[]
  productCategoryTypes: IProductCategoryType[]
  isLoadingAddValidation: boolean
  productValidations: ILoanValidation[]
  singleProductValidations: ISingleProductValidation[]
  isLoadingProductValidations: boolean
  bankBranches: IBankBranch[]
  isLoadingUpdateProduct: boolean
  userProducts: ILoanProduct[]
  isLoadingCreateCategory: boolean
  isLoadingUpdateCategory: boolean
  productToCopy: ILoanProduct
  productToView: ILoanProduct
  productToAssignValidations: ILoanProduct
  isCheckerViewProfileOpen: boolean
  isCheckRerunLoading: boolean
  isLoadingCancelRequest: boolean
  //documents upload
  isLoadingUploadDocument: boolean
  //Broker management
  brokersSummary: ILoanRequestsSummary
  isLoadingBrokers: boolean
  brokers: IBrokerCreate[]
  isLoadingCreateBroker: boolean
  isCreateBrokerSuccess: boolean
  isCreateBrokerFailure: boolean
  createdBroker: IBrokerCreate

  isLoadingUpdateBroker: boolean
  isUpdateBrokerSuccess: boolean
  isUpdateBrokerFailure: boolean

  isGenerateBrokerSecretLoading: boolean
  isGenerateBrokerSecretSuccess: boolean
  isGenerateBrokerSecretFailure: boolean
  selectedBrokerExisting: ISelectedBrokerExisting
  selectedBroker: IBrokerCreate

  isLoadingAssignBrokerProduct: boolean
  isSuccessAssignBrokerProduct: boolean
  isFailureAssignBrokerProduct: boolean
  brokerProducts: ILoanProduct[]
}
const initialState: ILoansState = {
  isViewingRequest: false,
  loanRequests: [],
  isLoadingLoans: false,
  loansCount: 0,
  pageNumber: 0,
  pageSize: 10,
  totalPageCount: 0,
  selectedRequest: {} as ILoanRequest,
  customerProfile: {} as ICustomerLoanProfile,
  isLoadingCustomerProfile: false,
  customerDocuments: [],
  customerChecks: [],
  loanRepayments: [],
  loanProducts: [],
  loanProductsSummary: {} as ILoanRequestsSummary,
  isLoadingLoanProducts: false,
  currentProduct: {} as ILoanProduct,
  isLoadingOverrideValidationCheck: false,
  organizations: [],
  organizationsSummary: {} as ILoanRequestsSummary,
  editOrganization: {} as IOrganization,
  isLoadingOrganizations: false,
  isLoadingCreateOrganization: false,
  isLoadingEditOrganization: false,
  listFilters: { status: '' },
  createdProduct: {} as ILoanProduct,
  isLoadingCreateProduct: false,
  loanValidations: [],
  kycTypeValidations: [],
  creditTypeValidations: [],
  productCategories: [],
  isLoadingAddValidation: false,
  productValidations: [],
  isLoadingProductValidations: false,
  bankBranches: [],
  isLoadingUpdateProduct: false,
  userProducts: [],
  isLoadingCreateCategory: false,
  isLoadingUpdateCategory: false,
  productToCopy: {} as ILoanProduct,
  productToView: {} as ILoanProduct,
  productCategoryTypes: [],
  productToAssignValidations: {} as ILoanProduct,
  singleProductValidations: [],
  organizationLimitReportResponse: {} as IOrganizationLimitResponse,
  isLoadingOrgLimitReports: false,
  isLoadingOrgLimitReportsExport: false,
  isSuccessOrgLimitReports: false,
  isSuccessOrgLimitReportsExport: false,
  loanReportRequestResponse: {} as ILoanRequestReportResponse,
  isLoadingReportRequest: false,
  isSuccessfulReportRequest: false,
  isLoadingGenerateRequestsReport: false,
  isSuccessfulGenerateRequestsReport: false,
  loanReportResponse: {} as ILoanReportResponse,
  isLoadingLoanReport: false,
  isSuccessfulLoanReport: false,
  isLoadingGenerateLoanReport: false,
  isSuccessfulGenerateLoanReport: false,
  requestReports: {} as IRequestCheckReportsResponse,
  isLoadingRequestReportsCheck: false,
  isLoadingRequestReportsCheckExport: false,
  isSuccessRequestReportsCheck: false,
  isSuccessRequestReportsCheckExport: false,
  isCheckerViewProfileOpen: false,
  isCheckRerunLoading: false,
  isLoadingCancelRequest: false,
  //documents upload
  isLoadingUploadDocument: false,
  //Broker management
  brokersSummary: {} as ILoanRequestsSummary,
  isLoadingBrokers: false,
  brokers: [],
  createdBroker: {} as IBrokerCreate,
  selectedBroker: {} as IBrokerCreate,
  isLoadingCreateBroker: false,
  isCreateBrokerSuccess: false,
  isCreateBrokerFailure: false,
  isLoadingUpdateBroker: false,
  isUpdateBrokerSuccess: false,
  isUpdateBrokerFailure: false,
  isLoadingAssignBrokerProduct: false,
  isSuccessAssignBrokerProduct: false,
  isFailureAssignBrokerProduct: false,

  isGenerateBrokerSecretLoading: false,
  isGenerateBrokerSecretSuccess: false,
  isGenerateBrokerSecretFailure: false,

  brokerProducts: [] as ILoanProduct[],

  selectedBrokerExisting: {} as ISelectedBrokerExisting,
}
const loansSlice = createSlice({
  name: 'loans',
  initialState,
  reducers: {
    setLoadingLoans(state, action: PayloadAction<boolean>) {
      state.isLoadingLoans = action.payload
    },
    setLoanRequests(state, action: PayloadAction<ILoanRequest[]>) {
      state.loanRequests = action.payload
    },
    setLoanRequestsSummary(state, action: PayloadAction<ILoanRequestsSummary>) {
      state.loansCount = action.payload.totalElements
      state.pageNumber = action.payload.pageNumber
      state.totalPageCount = action.payload.totalNumberOfPages
      state.pageSize = action.payload.pageSize
    },
    setIsViewingRequest(state, action: PayloadAction<boolean>) {
      state.isViewingRequest = action.payload
    },
    setSelectedRequest(state, action: PayloadAction<ILoanRequest>) {
      state.selectedRequest = action.payload
    },
    setCustomerProfile(state, action: PayloadAction<ICustomerLoanProfile>) {
      state.customerProfile = action.payload
    },
    setIsLoadingCustomerProfile(state, action: PayloadAction<boolean>) {
      state.isLoadingCustomerProfile = action.payload
    },
    setCustomerDocuments(state, action: PayloadAction<ICustomerDocument[]>) {
      state.customerDocuments = action.payload
    },
    setCustomerChecks(state, action: PayloadAction<ICustomerCheck[]>) {
      state.customerChecks = action.payload
    },
    setLoanRepaymentHistory(state, action: PayloadAction<IRepaymentHistory[]>) {
      state.loanRepayments = action.payload
    },
    setLoanProducts(state, action: PayloadAction<ILoanProduct[]>) {
      state.loanProducts = action.payload
    },
    setIsLoadingLoanProducts(state, action: PayloadAction<boolean>) {
      state.isLoadingLoanProducts = action.payload
    },
    setCurrentLoanProduct(state, action: PayloadAction<ILoanProduct>) {
      state.currentProduct = action.payload
    },
    setIsLoadingOverrideValidationCheck(state, action: PayloadAction<boolean>) {
      state.isLoadingOverrideValidationCheck = action.payload
    },
    setOrganizations(state, action: PayloadAction<IOrganization[]>) {
      state.organizations = action.payload
    },

    setEditOrganization(state, action: PayloadAction<IOrganization>) {
      state.editOrganization = action.payload
    },
    setIsLoadingOrganizations(state, action: PayloadAction<boolean>) {
      state.isLoadingOrganizations = action.payload
    },
    setLoadingCreateOrganization(state, action: PayloadAction<boolean>) {
      state.isLoadingCreateOrganization = action.payload
    },
    setListFilters(state, action: PayloadAction<{ status: string }>) {
      state.listFilters = action.payload
    },
    setCreatedProduct(state, action: PayloadAction<ILoanProduct>) {
      state.createdProduct = action.payload
    },
    setIsLoadingCreateProduct(state, action: PayloadAction<boolean>) {
      state.isLoadingCreateProduct = action.payload
    },
    setLoanValidations(state, action: PayloadAction<ILoanValidation[]>) {
      state.loanValidations = action.payload
      state.kycTypeValidations = action.payload.filter(
        (validation) => validation.type === 'KYC'
      )
      state.creditTypeValidations = action.payload.filter(
        (validation) => validation.type === 'CREDIT'
      )
    },
    setProductCategories(state, action: PayloadAction<IProductCategory[]>) {
      state.productCategories = action.payload
    },
    setProductCategoryTypes(
      state,
      action: PayloadAction<IProductCategoryType[]>
    ) {
      state.productCategoryTypes = action.payload
    },
    setIsLoadingAddValidation(state, action: PayloadAction<boolean>) {
      state.isLoadingAddValidation = action.payload
    },
    setProductValidations(state, action: PayloadAction<ILoanValidation[]>) {
      state.productValidations = action.payload
    },
    setIsLoadingProductValidations(state, action: PayloadAction<boolean>) {
      state.isLoadingProductValidations = action.payload
    },
    setBankBranches(state, action: PayloadAction<IBankBranch[]>) {
      state.bankBranches = action.payload
    },
    setIsLoadingUpdateProduct(state, action: PayloadAction<boolean>) {
      state.isLoadingUpdateProduct = action.payload
    },
    setUserProducts(state, action: PayloadAction<ILoanProduct[]>) {
      state.userProducts = action.payload
    },
    setIsLoadingCreateCategory(state, action: PayloadAction<boolean>) {
      state.isLoadingCreateCategory = action.payload
    },
    setIsLoadingUpdateCategory(state, action: PayloadAction<boolean>) {
      state.isLoadingUpdateCategory = action.payload
    },
    setProductToCopy(state, action: PayloadAction<ILoanProduct>) {
      state.productToCopy = action.payload
    },
    setProductToAssignValidations(state, action: PayloadAction<ILoanProduct>) {
      state.productToAssignValidations = action.payload
    },
    setProductToView(state, action: PayloadAction<ILoanProduct>) {
      state.productToView = action.payload
    },
    setSingleProductValidations(
      state,
      action: PayloadAction<ISingleProductValidation[]>
    ) {
      state.singleProductValidations = action.payload
    },
    setOrganizationLimitReport: (
      state,
      action: PayloadAction<IOrganizationLimitResponse>
    ) => {
      state.organizationLimitReportResponse = action.payload
    },
    setOrganizationLimitReportSuccess: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isSuccessOrgLimitReports = action.payload
    },
    setOrganizationLimitReportLoading: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isLoadingOrgLimitReports = action.payload
    },
    setOrganizationLimitReportExportLoading: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isLoadingOrgLimitReportsExport = action.payload
    },
    setOrganizationLimitReportExportSuccess: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isSuccessOrgLimitReportsExport = action.payload
    },
    setLoanRequestReportsResponse: (
      state,
      action: PayloadAction<ILoanRequestReportResponse>
    ) => {
      state.loanReportRequestResponse = action.payload
    },
    setLoanRequestReportLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoadingReportRequest = action.payload
    },
    setLoanRequestReportSuccess: (state, action: PayloadAction<boolean>) => {
      state.isSuccessfulReportRequest = action.payload
    },
    setLoanReports: (state, action: PayloadAction<ILoanReportResponse>) => {
      state.loanReportResponse = action.payload
    },
    setLoanReportLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoadingLoanReport = action.payload
    },
    setLoanReportsSuccess: (state, action: PayloadAction<boolean>) => {
      state.isSuccessfulLoanReport = action.payload
    },
    setGeneratedLoanReportSuccess: (state, action: PayloadAction<boolean>) => {
      state.isSuccessfulGenerateLoanReport = action.payload
    },
    setGeneratedRequestReportSuccess: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isSuccessfulGenerateRequestsReport = action.payload
    },
    setGeneratedRequestReportLoading: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isLoadingGenerateRequestsReport = action.payload
    },
    setGeneratedLoanReportLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoadingGenerateLoanReport = action.payload
    },

    setIsCheckerViewProfileOpen(state, action: PayloadAction<boolean>) {
      state.isCheckerViewProfileOpen = action.payload
    },
    setIsCheckRerunLoading(state, action: PayloadAction<boolean>) {
      state.isCheckRerunLoading = action.payload
    },
    setIsLoadingEditOrganization(state, action: PayloadAction<boolean>) {
      state.isLoadingEditOrganization = action.payload
    },
    setOrganizationsSummary(
      state,
      action: PayloadAction<ILoanRequestsSummary>
    ) {
      state.organizationsSummary = action.payload
    },
    setLoanProductsSummary(state, action: PayloadAction<ILoanRequestsSummary>) {
      state.loanProductsSummary = action.payload
    },
    setIsLoadingUploadDocument(state, action: PayloadAction<boolean>) {
      state.isLoadingUploadDocument = action.payload
    },
    setRequestCheckReportsResponse: (
      state,
      action: PayloadAction<IRequestCheckReportsResponse>
    ) => {
      state.requestReports = action.payload
    },
    setRequestCheckReportsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoadingRequestReportsCheck = action.payload
    },
    setRequestCheckReportsExportLoading: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isLoadingRequestReportsCheckExport = action.payload
    },
    setRequestCheckReportsSuccess: (state, action: PayloadAction<boolean>) => {
      state.isSuccessRequestReportsCheck = action.payload
    },
    setRequestCheckReportsExportSuccess: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isSuccessRequestReportsCheckExport = action.payload
    },
    setIsLoadingCancelRequest(state, action: PayloadAction<boolean>) {
      state.isLoadingCancelRequest = action.payload
    },
    //Broker management
    setBrokersSummary(state, action: PayloadAction<ILoanRequestsSummary>) {
      state.brokersSummary = action.payload
    },
    setIsLoadingBrokers(state, action: PayloadAction<boolean>) {
      state.isLoadingBrokers = action.payload
    },

    setBrokers(state, action: PayloadAction<IBrokerCreate[]>) {
      state.brokers = action.payload
    },
    setLoadingCreateBroker(state, action: PayloadAction<boolean>) {
      state.isLoadingCreateBroker = action.payload
    },
    setCreatedBroker: (state, action: PayloadAction<IBrokerCreate>) => {
      state.createdBroker = action.payload
    },

    setCreateBrokerSuccess(state, action: PayloadAction<boolean>) {
      state.isCreateBrokerSuccess = action.payload
    },
    setCreateBrokerFailure(state, action: PayloadAction<boolean>) {
      state.isCreateBrokerFailure = action.payload
    },

    setLoadingUpdateBroker: (state, action: PayloadAction<boolean>) => {
      state.isLoadingUpdateBroker = action.payload
    },
    setUpdateBrokerSuccess: (state, action: PayloadAction<boolean>) => {
      state.isUpdateBrokerSuccess = action.payload
    },
    setUpdateBrokerFailure: (state, action: PayloadAction<boolean>) => {
      state.isUpdateBrokerFailure = action.payload
    },

    setIsGenerateBrokerSecretLoading: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isGenerateBrokerSecretLoading = action.payload
    },
    setGenerateBrokerSecretSuccess: (state, action: PayloadAction<boolean>) => {
      state.isGenerateBrokerSecretSuccess = action.payload
    },

    setGenerateBrokerSecretFailure: (state, action: PayloadAction<boolean>) => {
      state.isGenerateBrokerSecretFailure = action.payload
    },

    setSelectedBrokerExisting: (
      state,
      action: PayloadAction<ISelectedBrokerExisting>
    ) => {
      state.selectedBrokerExisting = action.payload
    },

    setSelectedBroker: (state, action: PayloadAction<IBrokerCreate>) => {
      state.selectedBroker = action.payload
    },
    setAssignBrokerProductLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoadingAssignBrokerProduct = action.payload
    },
    setAssignBrokerProductSuccess: (state, action: PayloadAction<boolean>) => {
      state.isSuccessAssignBrokerProduct = action.payload
    },
    setAssignBrokerProductFailure: (state, action: PayloadAction<boolean>) => {
      state.isFailureAssignBrokerProduct = action.payload
    },
    setBrokerProducts: (state, action: PayloadAction<ILoanProduct[]>) => {
      state.brokerProducts = action.payload
    },
  },
})

export const {
  setLoadingLoans,
  setSelectedRequest,
  setIsViewingRequest,
  setLoanRequests,
  setLoanRequestsSummary,
  setCustomerProfile,
  setIsLoadingCustomerProfile,
  setCustomerDocuments,
  setCustomerChecks,
  setLoanRepaymentHistory,
  setIsLoadingLoanProducts,
  setLoanProducts,
  setCurrentLoanProduct,
  setIsLoadingOverrideValidationCheck,
  setOrganizations,
  setEditOrganization,
  setIsLoadingOrganizations,
  setLoadingCreateOrganization,
  setListFilters,
  setCreatedProduct,
  setIsLoadingCreateProduct,
  setLoanValidations,
  setProductCategories,
  setIsLoadingAddValidation,
  setProductValidations,
  setIsLoadingProductValidations,
  setBankBranches,
  setIsLoadingUpdateProduct,
  setUserProducts,
  setIsLoadingCreateCategory,
  setIsLoadingUpdateCategory,
  setProductToCopy,
  setProductCategoryTypes,
  setProductToAssignValidations,
  setSingleProductValidations,
  setOrganizationLimitReport,
  setOrganizationLimitReportExportLoading,
  setOrganizationLimitReportExportSuccess,
  setOrganizationLimitReportLoading,
  setOrganizationLimitReportSuccess,
  setLoanRequestReportsResponse,
  setLoanReportLoading,
  setLoanReports,
  setLoanReportsSuccess,
  setLoanRequestReportLoading,
  setLoanRequestReportSuccess,
  setGeneratedLoanReportSuccess,
  setGeneratedRequestReportSuccess,
  setGeneratedRequestReportLoading,
  setGeneratedLoanReportLoading,
  setRequestCheckReportsResponse,
  setRequestCheckReportsLoading,
  setRequestCheckReportsExportLoading,
  setRequestCheckReportsSuccess,
  setRequestCheckReportsExportSuccess,

  setProductToView,
  setIsCheckerViewProfileOpen,
  setIsCheckRerunLoading,
  setIsLoadingEditOrganization,
  setOrganizationsSummary,
  setLoanProductsSummary,
  setIsLoadingUploadDocument,
  setIsLoadingCancelRequest,
  //Broker management
  setBrokersSummary,
  setIsLoadingBrokers,
  setBrokers,
  setLoadingCreateBroker,
  setCreatedBroker,
  setCreateBrokerSuccess,
  setCreateBrokerFailure,
  setLoadingUpdateBroker,
  setUpdateBrokerSuccess,
  setUpdateBrokerFailure,
  setIsGenerateBrokerSecretLoading,
  setGenerateBrokerSecretSuccess,
  setGenerateBrokerSecretFailure,
  setSelectedBrokerExisting,
  setSelectedBroker,
  setAssignBrokerProductLoading,
  setAssignBrokerProductSuccess,
  setAssignBrokerProductFailure,
  setBrokerProducts,
} = loansSlice.actions
export default loansSlice.reducer
