export { C as ClientType, s as CustomerErrorResponse, F as FilterType, O as IAcceptRejectRestrictAccountApprovals, a4 as IAccountActivation, a3 as IAccountLinkingCompletion, P as IActivateCustomerProfile, L as IApproveCustomerPinReset, Q as IApproveRejectCustomerProfileActivation, D as IApproveRejectCustomerUpdate, a as IChannelModule, a0 as ICreateCustomerAccount, a1 as ICreateCustomerApprovals, a2 as ICreateCustomerDeactivate, b as ICustomer, z as ICustomerAccount, G as ICustomerAccountDetails, E as ICustomerAccountEventHistory, H as ICustomerAccountHistoryLogs, u as ICustomerAccountLink, v as ICustomerCreate, M as ICustomerPinDetails, R as ICustomerPinLog, T as ICustomerPinLogResponse, K as ICustomerPinReset, A as ICustomerProfileAccount, t as ICustomersDataResponse, n as ICustomersFilter, Z as IDeactivateCustomer, _ as IDeactivateCustomerApprovals, X as IDeactivateCustomerDeviceParams, I as IDecodeToken, y as IDevice, x as IDevicesResponse, $ as IFetchCustomerAccount, h as IFilter, g as IFilterOption, W as IGetCustomerDeviceDetail, U as IGetCustomerDevicesParams, V as IGetCustomerDevicesProps, r as IGetCustomerResponse, q as IGetCustomersRespons, e as IHeadCell, i as ILandingApps, J as ILogs, a5 as INotificationEventSettings, ab as INotificationEventSubscriberPayload, a6 as INotificationEventSubscribers, a7 as INotificationEventTemplates, ac as INotificationEventType, a8 as INotificationEvents, aa as INotificationEventsPayload, ad as INotificationEventsPerAccount, a9 as INotificationFrequencies, o as IPendingCustomersFilter, k as IPermission, l as IPermissionGroup, Y as IRejectCustomerDeviceParams, m as IResource, N as IRestrictAccountParams, d as IRole, p as ISetCustomerSearch, f as ISidebarConfigItem, j as ITableData, B as IUpdateCustomerDetails, c as IUser, w as IprofileAccountStoreIds, S as StoreOfValue } from '../customers-CSmGMD06.js';
import 'react';
import 'redux';

interface INotification {
    id: string;
    userId: string;
    initiatorFullName: string;
    title: string;
    content: string;
    message: string;
    isRead: boolean;
    dateCreated: Date;
    updatedAt: string;
}
interface IApiError {
    status: string;
    message: string;
}
declare const initialApiError: Readonly<IApiError>;

/**
 * <AUTHOR> Kinyoro on 03/12/2024
 */
interface PaginatedResponse<T> {
    totalElements: number;
    size: number;
    page: number;
    totalNumberOfPages: number;
    data: T[];
}

/**
 * <AUTHOR> Kinyoro on 12/12/2024
 */
interface TokenResponse {
    access_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
}

/**
 * <AUTHOR> Kinyoro on 19/12/2024
 */
interface ApiResponse<T> {
    status: string;
    message: string;
    data: T;
    errors: string[];
}

type Changes = {
    actionee: actionee;
    action: Action;
    previousState?: null | string;
    actionSubject: ActionSubject;
    date: DateEnum;
    time: Time;
    resource?: null | string;
    comment?: null | string;
    type: 'creation' | 'approval' | 'edited' | 'deletion';
};
type Action = 'create' | 'edit' | 'delete';
type ActionSubject = 'Patsheba Gikunda';
type actionee = 'John Doe' | 'Jane Smith';
type DateEnum = 'February 25, 2023 ' | 'February 26, 2023 ';
type Time = '10:00 PM' | '10:00 AM' | '11:00 AM';
declare const changes: Changes[];

export { type Action, type ActionSubject, type ApiResponse, type Changes, type DateEnum, type IApiError, type INotification, type PaginatedResponse, type Time, type TokenResponse, type actionee, changes, initialApiError };
