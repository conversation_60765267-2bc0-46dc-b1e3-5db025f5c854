import * as redux from 'redux';
import * as _reduxjs_toolkit from '@reduxjs/toolkit';
import { I as IDecodeToken, a as IChannelModule, b as ICustomer, c as IUser, d as IRole } from '../customers-CSmGMD06.js';
import 'react';

interface IAuth {
    isLoadingLogin: boolean;
    isLoginSuccess: boolean;
    isLoginError: boolean;
    loginErrorMessage: string;
    decodedToken: IDecodeToken;
    channelModules: IChannelModule[];
    userInfo: {
        username: string;
        email: string;
        phoneNumber: string;
    } | null;
}
declare const setIsLoadingLogin: _reduxjs_toolkit.ActionCreatorWithPayload<boolean, "auth/setIsLoadingLogin">;
declare const setDecodedToken: _reduxjs_toolkit.ActionCreatorWithPayload<IDecodeToken, "auth/setDecodedToken">;
declare const setIsLoginSuccess: _reduxjs_toolkit.ActionCreatorWithPayload<boolean, "auth/setIsLoginSuccess">;
declare const setIsLoginError: _reduxjs_toolkit.ActionCreatorWithPayload<boolean, "auth/setIsLoginError">;
declare const setChannelModules: _reduxjs_toolkit.ActionCreatorWithPayload<IChannelModule[], "auth/setChannelModules">;
declare const setLoginErrorMessage: _reduxjs_toolkit.ActionCreatorWithPayload<string, "auth/setLoginErrorMessage">;
declare const setCredentials: _reduxjs_toolkit.ActionCreatorWithPayload<any, "auth/setCredentials">;
declare const _default$3: redux.Reducer<IAuth>;

interface INavigationState {
    switchToCustomerDetails: {
        customer: ICustomer | null;
        open: boolean;
        isPendingCustomer: boolean;
    };
    switchToUserDetails: {
        user: IUser | null;
        open: boolean;
    };
    switchToRoleDetails: {
        role: IRole | null;
        open: boolean;
        type: string;
    };
    isSidebarCollapsed: boolean;
    documentToggle: {
        open: boolean;
        imageUrl: string;
    };
}
declare const setSwitchToUserDetails: _reduxjs_toolkit.ActionCreatorWithPayload<{
    open: boolean;
    user: IUser | null;
}, "navigation/setSwitchToUserDetails">;
declare const setSwitchToCustomerDetails: _reduxjs_toolkit.ActionCreatorWithPayload<any, "navigation/setSwitchToCustomerDetails">;
declare const setSwitchToRoleDetails: _reduxjs_toolkit.ActionCreatorWithPayload<any, "navigation/setSwitchToRoleDetails">;
declare const setSidebarCollapsed: _reduxjs_toolkit.ActionCreatorWithPayload<boolean, "navigation/setSidebarCollapsed">;
declare const setDocumentToggle: _reduxjs_toolkit.ActionCreatorWithPayload<{
    open: boolean;
    imageUrl: string;
}, "navigation/setDocumentToggle">;
declare const _default$2: redux.Reducer<INavigationState>;

interface ILocalNotification {
    message: string;
    type: ILocalNotificationType;
}
type ILocalNotificationType = 'info' | 'success' | 'warning' | 'error';
declare const setNotification: _reduxjs_toolkit.ActionCreatorWithPayload<ILocalNotification, "notifications/setNotification">;
declare const clearNotification: _reduxjs_toolkit.ActionCreatorWithoutPayload<"notifications/clearNotification">;
declare const _default$1: redux.Reducer<{
    localNotification: string;
    localNotificationType: ILocalNotificationType;
}>;

type TComponent = 'create_user' | 'create_role' | 'view_right' | 'change_log' | 'edit_role' | 'edit_user' | 'view_roles' | 'event_history' | 'security_question_history' | 'pin_history' | 'customer_accounts_history' | 'pending_approval_requests';
interface DrawerChildren {
    open: boolean;
    drawerChildren: {
        childType: TComponent;
        data?: string | null | IRole | IUser | IRole[] | {
            event: string;
            event_source: string;
            event_date: string;
            id: string;
        }[];
    } | null;
    header: string;
}
interface OverlayState {
    openUserChangeLogDrawer: boolean;
    drawer: DrawerChildren;
}
declare const setOpenUserChangeLogs: _reduxjs_toolkit.ActionCreatorWithPayload<any, "overlays/setOpenUserChangeLogs">;
declare const setDrawer: _reduxjs_toolkit.ActionCreatorWithPayload<DrawerChildren, "overlays/setDrawer">;
declare const resetDrawer: _reduxjs_toolkit.ActionCreatorWithoutPayload<"overlays/resetDrawer">;
declare const _default: redux.Reducer<OverlayState>;

declare const rootReducer: redux.Reducer<{
    navigation: INavigationState;
    notifications: {
        localNotification: string;
        localNotificationType: "info" | "success" | "warning" | "error";
    };
    auth: IAuth;
    overlay: OverlayState;
}, redux.UnknownAction, Partial<{
    navigation: INavigationState | undefined;
    notifications: {
        localNotification: string;
        localNotificationType: "info" | "success" | "warning" | "error";
    } | undefined;
    auth: IAuth | undefined;
    overlay: OverlayState | undefined;
}>>;

export { type IAuth, type ILocalNotification, type INavigationState, type OverlayState, _default$3 as authReducer, clearNotification, _default$2 as navigation, _default$1 as notifications, _default as overlays, resetDrawer, rootReducer, setChannelModules, setCredentials, setDecodedToken, setDocumentToggle, setDrawer, setIsLoadingLogin, setIsLoginError, setIsLoginSuccess, setLoginErrorMessage, setNotification, setOpenUserChangeLogs, setSidebarCollapsed, setSwitchToCustomerDetails, setSwitchToRoleDetails, setSwitchToUserDetails };
