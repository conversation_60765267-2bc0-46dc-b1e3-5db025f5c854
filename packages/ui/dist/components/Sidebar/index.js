import {
  useCustomRouter
} from "../../chunk-FW72AQQN.js";
import {
  InternalCollapsedLogo,
  InternalNavLogo,
  NavMenuIcon
} from "../../chunk-YMEOZMFS.js";
import {
  __spreadValues
} from "../../chunk-BBZEL7EG.js";

// src/components/Sidebar/Sidebar.tsx
import {
  Box,
  IconButton,
  Link,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Stack,
  styled,
  Tooltip
} from "@mui/material";
import { useEffect, useState } from "react";
import { usePathname, useSearchParams } from "next/navigation";

// src/utils/helpers.ts
var isNavMatch = (path, pathname, searchParams) => {
  const url = `${pathname}?${searchParams}`;
  const matchPath = (options, pathname2) => {
    const { path: path2, end } = options;
    const regex = new RegExp(`^${path2}${end ? "$" : ""}`, "i");
    return regex.test(pathname2);
  };
  return path && searchParams.toString().length > 0 ? path === url : path && searchParams.toString().length === 0 ? matchPath({ path, end: false }, pathname) : false;
};

// src/components/Sidebar/Sidebar.tsx
import { jsx, jsxs } from "react/jsx-runtime";
var ListItemStyle = styled(ListItemButton)(() => ({
  height: 48,
  position: "relative",
  display: "flex",
  flexDirection: "row",
  flexWrap: "nowrap",
  textTransform: "capitalize",
  color: "#2A3339",
  "&:hover": {
    backgroundColor: "#F3F4F5",
    color: "#2A3339"
  }
}));
var NavBarStyle = styled(Box)(() => ({
  backgroundColor: "#FAFAFA",
  paddingTop: "1.5%",
  minHeight: `100vh`,
  borderRight: "1px solid #E7E8E9",
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
  justifyContent: "space-between",
  gap: "10px"
}));
var ListItemIconStyle = styled(ListItemIcon)({
  width: 22,
  height: 22,
  display: "flex",
  alignItems: "center",
  justifyContent: "center"
});
function NavItem({
  item,
  active,
  collapsed
}) {
  const { title, path, icon } = item;
  const isActiveRoot = active(path);
  const activeRootStyle = {
    color: "#000A12",
    fontWeight: "fontWeightMedium",
    backgroundColor: "#E7E8E9",
    "&:before": { display: "flex" },
    "& $title": {
      fontWeight: 400
    },
    "& $icon": {
      color: "#000A12"
    },
    borderRadius: "4px"
  };
  const router = useCustomRouter();
  return /* @__PURE__ */ jsx(Link, { onClick: () => router.push(path), underline: "none", children: /* @__PURE__ */ jsxs(
    ListItemStyle,
    {
      disableGutters: true,
      sx: __spreadValues({}, isActiveRoot && activeRootStyle),
      children: [
        /* @__PURE__ */ jsx(Tooltip, { title: collapsed ? title : "", placement: "right", children: /* @__PURE__ */ jsx(ListItemIconStyle, { children: icon && icon }) }),
        !collapsed && /* @__PURE__ */ jsx(
          ListItemText,
          {
            disableTypography: true,
            primary: title,
            sx: {
              minWidth: "150px"
            }
          }
        )
      ]
    }
  ) });
}
var Sidebar = ({
  sidebarConfig,
  sidebarCollapsed,
  bgColor = "#FAFAFA",
  linkHref = "/landing",
  footer,
  logo = /* @__PURE__ */ jsx(InternalNavLogo, {}),
  collapsedLogo = /* @__PURE__ */ jsx(InternalCollapsedLogo, {})
}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const isProductionEnv = process.env.NEXT_PUBLIC_ENVIRONMENT === "prod";
  const isUATEnv = process.env.NEXT_PUBLIC_ENVIRONMENT === "uat";
  const handleCollapse = () => {
    setCollapsed(!collapsed);
    sidebarCollapsed(!collapsed);
  };
  const [collapsed, setCollapsed] = useState(false);
  const handleResize = () => {
    const shouldCollapse = window.innerWidth < 1300;
    setCollapsed(shouldCollapse);
    sidebarCollapsed(shouldCollapse);
  };
  useEffect(() => {
    if (typeof window !== "undefined") {
      window.addEventListener("resize", handleResize);
      handleResize();
      return () => window.removeEventListener("resize", handleResize);
    }
  }, []);
  useEffect(() => {
    if (pathname === "/reports") {
      handleCollapse();
    }
  }, [pathname]);
  return /* @__PURE__ */ jsxs(
    NavBarStyle,
    {
      sx: {
        backgroundColor: bgColor,
        minWidth: collapsed ? "5vw" : "14rem",
        px: collapsed ? "1%" : "2%",
        transition: "min-width 0.3s, padding 0.3s"
      },
      children: [
        /* @__PURE__ */ jsxs(Stack, { alignItems: collapsed ? "center" : "flex-start", children: [
          /* @__PURE__ */ jsx(
            Link,
            {
              href: linkHref,
              sx: {
                "&:hover": {
                  backgroundColor: "transparent"
                },
                padding: "0",
                paddingLeft: "3.5%",
                transition: "all 0.3s",
                width: collapsed ? "44px" : "123px",
                height: collapsed ? "44px" : "50px",
                "& svg": {
                  width: "100%",
                  height: "100%"
                }
              },
              children: collapsed ? collapsedLogo : logo
            }
          ),
          /* @__PURE__ */ jsx(
            Tooltip,
            {
              title: collapsed ? "Expand Menu" : "Collapse Menu",
              placement: "right",
              children: /* @__PURE__ */ jsx(
                IconButton,
                {
                  onClick: handleCollapse,
                  sx: {
                    "&:hover": {
                      backgroundColor: "transparent"
                    }
                  },
                  children: /* @__PURE__ */ jsx(NavMenuIcon, {})
                }
              )
            }
          ),
          /* @__PURE__ */ jsx(Box, { children: /* @__PURE__ */ jsx(List, { disablePadding: true, children: sidebarConfig.map((item) => {
            if (isProductionEnv && item.isProductionReady || // Only show if production is ready
            isUATEnv && item.isUATReady !== false || // Show if UAT is not explicitly disabled
            !isProductionEnv && !isUATEnv) {
              return /* @__PURE__ */ jsx(
                NavItem,
                {
                  item,
                  active: () => isNavMatch(item.path, pathname, searchParams),
                  collapsed
                },
                item.title
              );
            }
            return null;
          }) }) })
        ] }),
        footer && footer
      ]
    }
  );
};
export {
  Sidebar
};
